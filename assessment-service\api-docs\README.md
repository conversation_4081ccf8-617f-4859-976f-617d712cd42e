# Assessment Service API Documentation

## Overview
Assessment Service adalah layanan untuk memproses data assessment psikologi (RIASEC, OCEAN, VIA-IS) dan menganalisisnya menggunakan AI untuk menghasilkan profil persona dan rekomendasi karir.

**Base URL**: `http://localhost:3003` (development)

## Error Codes
- **VALIDATION_ERROR** (400): Data input tidak valid atau format salah
- **UNAUTHORIZED** (401): Token autentikasi tidak valid atau tidak ada
- **FORBIDDEN** (403): A<PERSON><PERSON> di<PERSON><PERSON> (tidak memiliki permission atau token balance tidak cukup)
- **NOT_FOUND** (404): Resource tidak ditemukan
- **INTERNAL_ERROR** (500): Error internal server
- **SERVICE_UNAVAILABLE** (503): Service tidak tersedia

---

## Authentication
Semua endpoint (kecuali health dan test) memerlukan JWT token di header:
```
Authorization: Bearer <jwt_token>
```

---

## Main Endpoints

### POST /assessments/submit
**Deskripsi**: Submit data assessment untuk dianalisis AI
**Authentication**: Required
**Token Cost**: 1 token

**Request Body**:
```json
{
  "riasec": {
    "realistic": 75,
    "investigative": 85,
    "artistic": 60,
    "social": 70,
    "enterprising": 80,
    "conventional": 65
  },
  "ocean": {
    "openness": 85,
    "conscientiousness": 75,
    "extraversion": 70,
    "agreeableness": 80,
    "neuroticism": 40
  },
  "viaIs": {
    "creativity": 85,
    "curiosity": 90,
    "judgment": 75,
    "loveOfLearning": 80,
    "perspective": 70,
    "bravery": 65,
    "perseverance": 85,
    "honesty": 90,
    "zest": 75,
    "love": 80,
    "kindness": 85,
    "socialIntelligence": 70,
    "teamwork": 75,
    "fairness": 85,
    "leadership": 80,
    "forgiveness": 70,
    "humility": 75,
    "prudence": 80,
    "selfRegulation": 75,
    "appreciationOfBeauty": 70,
    "gratitude": 85,
    "hope": 80,
    "humor": 75,
    "spirituality": 60
  }
}
```

**Response** (202):
```json
{
  "success": true,
  "message": "Assessment submitted successfully and queued for analysis",
  "data": {
    "jobId": "uuid-string",
    "status": "queued",
    "estimatedProcessingTime": "2-5 minutes",
    "queuePosition": 3,
    "tokenCost": 1,
    "remainingTokens": 9
  }
}
```

### GET /assessments/status/:jobId
**Deskripsi**: Cek status pemrosesan assessment
**Authentication**: Required
**Parameters**: 
- `jobId` (path): UUID job yang dikembalikan dari submit

**Response** (200):
```json
{
  "success": true,
  "message": "Job status retrieved successfully",
  "data": {
    "jobId": "uuid-string",
    "status": "processing",
    "progress": 25,
    "estimatedTimeRemaining": "1-3 minutes",
    "createdAt": "2024-01-01T10:00:00.000Z",
    "updatedAt": "2024-01-01T10:01:00.000Z"
  }
}
```

**Status Values**:
- `queued`: Dalam antrian
- `processing`: Sedang diproses
- `completed`: Selesai (hasil tersedia di archive-service)
- `failed`: Gagal diproses

### GET /assessments/queue/status
**Deskripsi**: Mendapatkan status antrian untuk monitoring
**Authentication**: Required

**Response** (200):
```json
{
  "success": true,
  "message": "Queue status retrieved successfully",
  "data": {
    "queueLength": 5,
    "averageProcessingTime": "3.2 minutes",
    "estimatedWaitTime": "5-10 minutes",
    "jobStats": {
      "total": 15,
      "queued": 5,
      "processing": 2,
      "completed": 7,
      "failed": 1
    }
  }
}
```

---

## Health Check Endpoints

### GET /health
**Deskripsi**: Status kesehatan service dan dependencies
**Authentication**: None

**Response** (200):
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T10:00:00.000Z",
  "service": "assessment-service",
  "version": "1.0.0",
  "dependencies": {
    "rabbitmq": {
      "status": "healthy",
      "details": {
        "messageCount": 5,
        "consumerCount": 1
      }
    },
    "authService": {
      "status": "healthy"
    }
  },
  "jobs": {
    "total": 15,
    "queued": 5,
    "processing": 2,
    "completed": 7,
    "failed": 1
  },
  "system": {
    "uptime": 3600,
    "memory": {...},
    "hostname": "server-name",
    "platform": "linux",
    "nodeVersion": "v18.17.0"
  }
}
```

### GET /health/ready
**Deskripsi**: Readiness probe untuk container orchestration
**Authentication**: None

**Response** (200):
```json
{
  "status": "ready",
  "timestamp": "2024-01-01T10:00:00.000Z"
}
```

---

## Development/Test Endpoints

### POST /test/submit
**Deskripsi**: Submit assessment untuk testing (development only)
**Authentication**: None
**Environment**: Development only

**Request Body**: Same as `/assessments/submit`

**Response** (202):
```json
{
  "success": true,
  "message": "Test assessment submitted successfully and queued for analysis",
  "data": {
    "jobId": "uuid-string",
    "analysisId": "uuid-string",
    "status": "queued",
    "estimatedProcessingTime": "2-5 minutes",
    "queuePosition": 3,
    "userId": "test-uuid",
    "userEmail": "<EMAIL>"
  }
}
```

### GET /test/status/:jobId
**Deskripsi**: Cek status test assessment
**Authentication**: None
**Environment**: Development only

**Response**: Same format as `/assessments/status/:jobId`

---

## Root Endpoint

### GET /
**Deskripsi**: Service info
**Authentication**: None

**Response** (200):
```json
{
  "success": true,
  "message": "ATMA Assessment Service is running",
  "version": "1.0.0",
  "timestamp": "2024-01-01T10:00:00.000Z"
}
```

---

## Data Validation

### Assessment Data Requirements
- **RIASEC**: 6 dimensi (realistic, investigative, artistic, social, enterprising, conventional)
- **OCEAN**: 5 dimensi (openness, conscientiousness, extraversion, agreeableness, neuroticism)  
- **VIA-IS**: 24 character strengths (creativity, curiosity, judgment, dll.)
- **Score Range**: 0-100 (integer)
- **All fields required**: Semua field dalam setiap assessment wajib diisi

### Response Format
Semua response menggunakan format standar:
```json
{
  "success": boolean,
  "message": "string",
  "data": object
}
```

Untuk error:
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": object
  }
}
```
